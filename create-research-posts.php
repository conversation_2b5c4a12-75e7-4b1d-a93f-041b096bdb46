<?php
/**
 * Temporary script to create 10 research posts
 * Run this once and then delete the file
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// Sample research data
$research_posts = array(
    array(
        'title' => 'Breast Cancer Immunotherapy Research',
        'content' => 'This groundbreaking research focuses on developing new immunotherapy treatments for breast cancer patients. Our team is investigating novel approaches to enhance the immune system\'s ability to target and destroy cancer cells.',
        'cancer_type' => 'breast-cancer',
        'status' => 'current',
        'hero_title' => 'Advancing Breast Cancer Treatment',
        'hero_summary' => 'Pioneering immunotherapy research for better patient outcomes.',
        'image_url' => 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=600&fit=crop'
    ),
    array(
        'title' => 'Lung Cancer Early Detection Study',
        'content' => 'Our research team is developing advanced screening methods for early detection of lung cancer. This study aims to improve survival rates through earlier intervention and treatment.',
        'cancer_type' => 'lung-cancer',
        'status' => 'current',
        'hero_title' => 'Early Detection Saves Lives',
        'hero_summary' => 'Revolutionary screening methods for lung cancer detection.',
        'image_url' => 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800&h=600&fit=crop'
    ),
    array(
        'title' => 'Colorectal Cancer Prevention Research',
        'content' => 'This comprehensive study examines lifestyle factors and genetic markers that contribute to colorectal cancer development. Our goal is to develop effective prevention strategies.',
        'cancer_type' => 'colorectal-cancer',
        'status' => 'completed',
        'hero_title' => 'Prevention Through Research',
        'hero_summary' => 'Understanding risk factors to prevent colorectal cancer.',
        'image_url' => 'https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=800&h=600&fit=crop'
    ),
    array(
        'title' => 'Pediatric Brain Tumor Treatment Study',
        'content' => 'Our specialized research focuses on developing safer and more effective treatments for brain tumors in children. We are exploring targeted therapies with fewer side effects.',
        'cancer_type' => 'brain-cancer',
        'status' => 'current',
        'hero_title' => 'Hope for Young Patients',
        'hero_summary' => 'Developing gentler treatments for pediatric brain tumors.',
        'image_url' => 'https://images.unsplash.com/photo-**********-2a8555f1a136?w=800&h=600&fit=crop'
    ),
    array(
        'title' => 'Prostate Cancer Precision Medicine',
        'content' => 'This research project aims to personalize prostate cancer treatment based on individual genetic profiles. We are developing precision medicine approaches for better outcomes.',
        'cancer_type' => 'prostate-cancer',
        'status' => 'current',
        'hero_title' => 'Personalized Cancer Care',
        'hero_summary' => 'Tailoring prostate cancer treatment to individual patients.',
        'image_url' => 'https://images.unsplash.com/photo-**********-0eb30cd8c063?w=800&h=600&fit=crop'
    ),
    array(
        'title' => 'Ovarian Cancer Biomarker Discovery',
        'content' => 'Our team is identifying new biomarkers for ovarian cancer that could lead to earlier diagnosis and more targeted treatments. This research has significant potential for improving patient outcomes.',
        'cancer_type' => 'ovarian-cancer',
        'status' => 'completed',
        'hero_title' => 'Discovering New Biomarkers',
        'hero_summary' => 'Finding molecular signatures for better ovarian cancer care.',
        'image_url' => 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?w=800&h=600&fit=crop'
    ),
    array(
        'title' => 'Skin Cancer Prevention Education Program',
        'content' => 'This community-based research evaluates the effectiveness of educational programs in preventing skin cancer. We are studying behavior change and awareness campaigns.',
        'cancer_type' => 'skin-cancer',
        'status' => 'current',
        'hero_title' => 'Education Prevents Cancer',
        'hero_summary' => 'Community programs to reduce skin cancer incidence.',
        'image_url' => 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=600&fit=crop'
    ),
    array(
        'title' => 'Leukemia Stem Cell Research',
        'content' => 'Our laboratory research focuses on understanding leukemia stem cells and developing targeted therapies. This work could lead to more effective treatments for blood cancers.',
        'cancer_type' => 'blood-cancer',
        'status' => 'current',
        'hero_title' => 'Targeting Cancer Stem Cells',
        'hero_summary' => 'Revolutionary approach to treating leukemia.',
        'image_url' => 'https://images.unsplash.com/photo-1582719471384-894fbb16e074?w=800&h=600&fit=crop'
    ),
    array(
        'title' => 'Pancreatic Cancer Drug Development',
        'content' => 'This research project involves developing new drug combinations for pancreatic cancer treatment. We are testing innovative therapeutic approaches in clinical trials.',
        'cancer_type' => 'pancreatic-cancer',
        'status' => 'completed',
        'hero_title' => 'New Hope for Pancreatic Cancer',
        'hero_summary' => 'Developing breakthrough treatments for a challenging cancer.',
        'image_url' => 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=600&fit=crop'
    ),
    array(
        'title' => 'Cancer Survivorship Quality of Life Study',
        'content' => 'Our research examines the long-term effects of cancer treatment on survivors\' quality of life. We are developing support programs and interventions to improve post-treatment outcomes.',
        'cancer_type' => 'general',
        'status' => 'current',
        'hero_title' => 'Supporting Cancer Survivors',
        'hero_summary' => 'Improving quality of life after cancer treatment.',
        'image_url' => 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800&h=600&fit=crop'
    )
);

// Function to create research posts
function create_research_posts($posts_data) {
    $created_posts = array();

    foreach ($posts_data as $post_data) {
        // Create the post
        $post_args = array(
            'post_title' => $post_data['title'],
            'post_content' => $post_data['content'],
            'post_status' => 'draft', // Create as draft first
            'post_type' => 'research',
            'post_author' => 1, // Adjust as needed
        );

        $post_id = wp_insert_post($post_args);

        if ($post_id && !is_wp_error($post_id)) {
            // Set cancer type taxonomy
            if (isset($post_data['cancer_type'])) {
                wp_set_object_terms($post_id, $post_data['cancer_type'], 'cancer-type');
            }

            // Set status taxonomy
            if (isset($post_data['status'])) {
                wp_set_object_terms($post_id, $post_data['status'], 'current-or-completed');
            }

            // Set ACF fields (hero section)
            if (isset($post_data['hero_title']) && isset($post_data['hero_summary'])) {
                update_field('hero_section', array(
                    'hero_title' => $post_data['hero_title'],
                    'hero_summary' => $post_data['hero_summary']
                ), $post_id);
            }

            // Set listing summary
            update_field('listing_summary', substr($post_data['content'], 0, 150) . '...', $post_id);

            // Set featured image from URL
            if (isset($post_data['image_url'])) {
                $image_id = create_attachment_from_url($post_data['image_url'], $post_id);
                if ($image_id) {
                    set_post_thumbnail($post_id, $image_id);
                    update_field('listing_image', $image_id, $post_id);
                }
            }

            $created_posts[] = array(
                'id' => $post_id,
                'title' => $post_data['title']
            );

            echo "Created research post: " . $post_data['title'] . " (ID: $post_id)\n";
        } else {
            echo "Failed to create post: " . $post_data['title'] . "\n";
        }
    }

    return $created_posts;
}

// Function to create attachment from URL
function create_attachment_from_url($url, $post_id = 0) {
    $upload_dir = wp_upload_dir();
    $image_data = file_get_contents($url);

    if ($image_data === false) {
        return false;
    }

    $filename = basename($url);
    if (strpos($filename, '?') !== false) {
        $filename = substr($filename, 0, strpos($filename, '?'));
    }
    if (empty($filename)) {
        $filename = 'research-image-' . time() . '.jpg';
    }

    $file = $upload_dir['path'] . '/' . $filename;
    file_put_contents($file, $image_data);

    $wp_filetype = wp_check_filetype($filename, null);
    $attachment = array(
        'post_mime_type' => $wp_filetype['type'],
        'post_title' => sanitize_file_name($filename),
        'post_content' => '',
        'post_status' => 'inherit'
    );

    $attach_id = wp_insert_attachment($attachment, $file, $post_id);
    require_once(ABSPATH . 'wp-admin/includes/image.php');
    $attach_data = wp_generate_attachment_metadata($attach_id, $file);
    wp_update_attachment_metadata($attach_id, $attach_data);

    return $attach_id;
}

// Check if running from command line
if (php_sapi_name() === 'cli') {
    // Running from terminal
    echo "Creating Research Posts...\n";
    $created = create_research_posts($research_posts);
    echo "Created " . count($created) . " research posts successfully!\n";
    echo "Remember to delete this file after use!\n";
} elseif (isset($_GET['create']) && $_GET['create'] === 'research') {
    // Running from browser
    echo "<h2>Creating Research Posts...</h2>\n";
    $created = create_research_posts($research_posts);
    echo "<h3>Created " . count($created) . " research posts successfully!</h3>\n";
    echo "<p><strong>Remember to delete this file after use!</strong></p>\n";
} else {
    // Browser interface
    echo "<h2>Research Post Creator</h2>\n";
    echo "<p>This script will create 10 sample research posts.</p>\n";
    echo "<p><a href='?create=research'>Click here to create the posts</a></p>\n";
    echo "<p><strong>Warning: This will create actual posts in your database!</strong></p>\n";
}
?>
