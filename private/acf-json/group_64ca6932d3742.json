{"key": "group_64ca6932d3742", "title": "Block: Advanced Donation", "fields": [{"key": "field_64ca6997bf1a8", "label": "Background colour", "name": "background_colour", "aria-label": "", "type": "select", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"default": "<PERSON><PERSON><PERSON>", "secondary": "Secondary"}, "default_value": "default", "return_format": "value", "multiple": 0, "allow_null": 0, "ui": 0, "ajax": 0, "placeholder": ""}, {"key": "field_65a56932f7db9", "label": "Anchor ID", "name": "anchor_id", "aria-label": "", "type": "text", "instructions": "The anchor ID entered here can be used within the Page Index block. Anchor IDs must not contain any spaces.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_64ca6a947547c", "label": "Image", "name": "image", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "id", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "jpg,jpeg,png", "preview_size": "medium"}, {"key": "field_64ca69333f1fd", "label": "Title", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_64ca694d80703", "label": "Summary", "name": "summary", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "rows": "", "placeholder": "", "new_lines": ""}, {"key": "field_64ca6a547547b", "label": "Donation type", "name": "donation_type", "aria-label": "", "type": "select", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"once": "Once", "monthly": "Monthly", "both": "Both"}, "default_value": "both", "return_format": "value", "multiple": 0, "allow_null": 0, "ui": 0, "ajax": 0, "placeholder": ""}, {"key": "field_64ca6b487547e", "label": "Single donation suggested amounts", "name": "single_donation_suggested_amounts", "aria-label": "", "type": "repeater", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_64ca6a547547b", "operator": "==", "value": "once"}], [{"field": "field_64ca6a547547b", "operator": "==", "value": "both"}]], "wrapper": {"width": "", "class": "", "id": ""}, "layout": "table", "pagination": 0, "min": 1, "max": 3, "collapsed": "", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_64ca6b7f75480", "label": "Amount", "name": "amount", "aria-label": "", "type": "number", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "min": "", "max": "", "placeholder": "", "step": "", "prepend": "", "append": "", "parent_repeater": "field_64ca6b487547e"}, {"key": "field_64ca6b8d75481", "label": "Description", "name": "description", "aria-label": "", "type": "textarea", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "relevanssi_exclude": 0, "default_value": "", "maxlength": "", "allow_in_bindings": 1, "rows": "", "placeholder": "", "new_lines": "", "parent_repeater": "field_64ca6b487547e"}]}, {"key": "field_64ca6bb175482", "label": "Monthly donation suggested amounts", "name": "monthly_donation_suggested_amounts", "aria-label": "", "type": "repeater", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_64ca6a547547b", "operator": "==", "value": "monthly"}], [{"field": "field_64ca6a547547b", "operator": "==", "value": "both"}]], "wrapper": {"width": "", "class": "", "id": ""}, "layout": "table", "pagination": 0, "min": 1, "max": 3, "collapsed": "", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_64ca6bb175483", "label": "Amount", "name": "amount", "aria-label": "", "type": "number", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "min": "", "max": "", "placeholder": "", "step": "", "prepend": "", "append": "", "parent_repeater": "field_64ca6bb175482"}, {"key": "field_64ca6bb175484", "label": "Description", "name": "description", "aria-label": "", "type": "textarea", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "relevanssi_exclude": 0, "default_value": "", "maxlength": "", "allow_in_bindings": 1, "rows": "", "placeholder": "", "new_lines": "", "parent_repeater": "field_64ca6bb175482"}]}, {"key": "field_682b299e186bc", "label": "Amount description", "name": "amount_description", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "relevanssi_exclude": 0, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "rows": "", "placeholder": "", "new_lines": ""}, {"key": "field_64ca6b337547d", "label": "Campaign ID", "name": "campaign_id", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_6633819bdbb37", "label": "Fund ID", "name": "fund_id", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "relevanssi_exclude": 0, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}], "location": [[{"param": "block", "operator": "==", "value": "acf/advanced-donation"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1748418981}