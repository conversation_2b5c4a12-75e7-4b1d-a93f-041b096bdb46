[{"key": "group_front_page_featured", "title": "Optional Featured Promo", "fields": [{"key": "field_front_page_bg_color", "label": "Background colour", "name": "front_page_bg_color", "aria-label": "", "type": "select", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"default": "<PERSON><PERSON><PERSON>", "secondary": "Secondary"}, "default_value": "default", "return_format": "value", "multiple": 0, "allow_null": 0, "ui": 0, "ajax": 0, "placeholder": ""}, {"key": "field_front_page_alignment", "label": "Alignment", "name": "front_page_alignment", "aria-label": "", "type": "select", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"inline": "Inline", "full-width": "Full width"}, "default_value": "full-width", "return_format": "value", "multiple": 0, "allow_null": 0, "ui": 0, "ajax": 0, "placeholder": ""}, {"key": "field_front_page_anchor_id", "label": "Anchor ID", "name": "front_page_anchor_id", "aria-label": "", "type": "text", "instructions": "The anchor ID entered here can be used within the Page Index block. Anchor IDs must not contain any spaces.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_front_page_title", "label": "Title", "name": "front_page_title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_front_page_summary", "label": "Summary", "name": "front_page_summary", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "rows": "", "placeholder": "", "new_lines": ""}, {"key": "field_front_page_content_type", "label": "Type", "name": "front_page_content_type", "aria-label": "", "type": "radio", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"manual": "Manual", "automatic": "Automatic"}, "default_value": "manual", "return_format": "value", "allow_null": 0, "other_choice": 0, "layout": "vertical", "save_other_choice": 0}, {"key": "field_front_page_display_title_only", "label": "Display title only", "name": "display_title_only", "aria-label": "", "type": "true_false", "instructions": "Create more compact presentations of related contents on a page where scroll may need to be reduced.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_front_page_change_layout", "label": "Change layout", "name": "change_layout", "aria-label": "", "type": "true_false", "instructions": "Toggle to change layout of this component. For better output make sure you have 4 posts selected or created", "required": 0, "conditional_logic": [[{"field": "field_front_page_content_type", "operator": "==", "value": "manual"}, {"field": "field_front_page_alignment", "operator": "==", "value": "full-width"}]], "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_front_page_automatic_promos", "label": "Automatic promos", "name": "front_page_automatic_promos", "aria-label": "", "type": "group", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_front_page_content_type", "operator": "==", "value": "automatic"}]], "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_front_page_content_type_select", "label": "Content type", "name": "content_type", "aria-label": "", "type": "select", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"profiles": "Profiles", "update": "Updates", "library": "Library", "event": "Events"}, "default_value": false, "return_format": "value", "multiple": 0, "allow_null": 0, "ui": 0, "ajax": 0, "placeholder": ""}, {"key": "field_front_page_taxonomies", "label": "Taxonomies", "name": "taxonomies", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_front_page_update_types", "label": "Update types", "name": "update_types", "aria-label": "", "type": "taxonomy", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_front_page_content_type_select", "operator": "==", "value": "update"}]], "wrapper": {"width": "", "class": "", "id": ""}, "taxonomy": "update-type", "add_term": 0, "save_terms": 0, "load_terms": 0, "return_format": "id", "field_type": "multi_select", "allow_null": 0, "multiple": 0}, {"key": "field_front_page_update_topics", "label": "Update topics", "name": "update_topics", "aria-label": "", "type": "taxonomy", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_front_page_content_type_select", "operator": "==", "value": "update"}]], "wrapper": {"width": "", "class": "", "id": ""}, "taxonomy": "topic", "add_term": 0, "save_terms": 0, "load_terms": 0, "return_format": "id", "field_type": "multi_select", "allow_null": 0, "multiple": 0}, {"key": "field_front_page_update_themes", "label": "Update themes", "name": "update_themes", "aria-label": "", "type": "taxonomy", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_front_page_content_type_select", "operator": "==", "value": "update"}]], "wrapper": {"width": "", "class": "", "id": ""}, "taxonomy": "themes", "add_term": 0, "save_terms": 0, "load_terms": 0, "return_format": "id", "field_type": "multi_select", "allow_null": 0, "multiple": 0}, {"key": "field_front_page_event_categories", "label": "Event categories", "name": "event_categories", "aria-label": "", "type": "taxonomy", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_front_page_content_type_select", "operator": "==", "value": "event"}]], "wrapper": {"width": "", "class": "", "id": ""}, "taxonomy": "event-category", "add_term": 0, "save_terms": 0, "load_terms": 0, "return_format": "id", "field_type": "multi_select", "allow_null": 0, "multiple": 0}, {"key": "field_front_page_event_types", "label": "Event types", "name": "event_types", "aria-label": "", "type": "taxonomy", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_front_page_content_type_select", "operator": "==", "value": "event"}]], "wrapper": {"width": "", "class": "", "id": ""}, "taxonomy": "event-type", "add_term": 0, "save_terms": 0, "load_terms": 0, "return_format": "id", "field_type": "multi_select", "allow_null": 0, "multiple": 0}, {"key": "field_front_page_event_locations", "label": "Event locations", "name": "event_locations", "aria-label": "", "type": "taxonomy", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_front_page_content_type_select", "operator": "==", "value": "event"}]], "wrapper": {"width": "", "class": "", "id": ""}, "taxonomy": "location", "add_term": 0, "save_terms": 0, "load_terms": 0, "return_format": "id", "field_type": "multi_select", "allow_null": 0, "multiple": 0}, {"key": "field_front_page_library_types", "label": "Library types", "name": "library_types", "aria-label": "", "type": "taxonomy", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_front_page_content_type_select", "operator": "==", "value": "library"}]], "wrapper": {"width": "", "class": "", "id": ""}, "taxonomy": "library-type", "add_term": 0, "save_terms": 0, "load_terms": 0, "return_format": "id", "field_type": "multi_select", "allow_null": 0, "multiple": 0}, {"key": "field_front_page_profile_types", "label": "Profile types", "name": "profile_types", "aria-label": "", "type": "taxonomy", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_front_page_content_type_select", "operator": "==", "value": "profiles"}]], "wrapper": {"width": "", "class": "", "id": ""}, "taxonomy": "profile-type", "add_term": 0, "save_terms": 0, "load_terms": 0, "return_format": "id", "field_type": "multi_select", "allow_null": 0, "multiple": 0}]}, {"key": "field_front_page_quantity", "label": "Quantity", "name": "quantity", "aria-label": "", "type": "number", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "min": 1, "max": 12, "placeholder": "", "step": "", "prepend": "", "append": ""}, {"key": "field_front_page_sort_by", "label": "Sort by", "name": "sort_by", "aria-label": "", "type": "select", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"date": "Publication date", "title": "Alphabetical", "event_dates_date_from": "Event start date"}, "default_value": "date", "return_format": "value", "multiple": 0, "allow_null": 0, "ui": 0, "ajax": 0, "placeholder": ""}]}, {"key": "field_front_page_manual_content", "label": "Manual promos", "name": "front_page_manual_content", "aria-label": "", "type": "group", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_front_page_content_type", "operator": "==", "value": "manual"}]], "wrapper": {"width": "", "class": "", "id": ""}, "layout": "row", "sub_fields": [{"key": "field_front_page_manual_type", "label": "Type", "name": "type", "aria-label": "", "type": "radio", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"cms": "Select from CMS", "manual": "Manual input"}, "default_value": "cms", "return_format": "value", "allow_null": 0, "other_choice": 0, "layout": "vertical", "save_other_choice": 0}, {"key": "field_front_page_selected_content", "label": "Select content", "name": "selected_content", "aria-label": "", "type": "repeater", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_front_page_manual_type", "operator": "==", "value": "cms"}]], "wrapper": {"width": "", "class": "", "id": ""}, "layout": "row", "min": 1, "max": 12, "collapsed": "", "button_label": "Add Row", "sub_fields": [{"key": "field_front_page_page", "label": "Page", "name": "page", "aria-label": "", "type": "post_object", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "post_type": "", "post_status": "", "taxonomy": "", "return_format": "id", "multiple": 0, "allow_null": 0, "ui": 1}, {"key": "field_front_page_override_fields", "label": "Override fields", "name": "override_fields", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 0, "ui": 1}, {"key": "field_front_page_title_override", "label": "Title override", "name": "title_override", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_front_page_override_fields", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_front_page_summary_override", "label": "Summary override", "name": "summary_override", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_front_page_override_fields", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "rows": "", "placeholder": "", "new_lines": ""}, {"key": "field_front_page_image_override", "label": "Image override", "name": "image_override", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_front_page_override_fields", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "id", "library": "all", "preview_size": "medium"}]}, {"key": "field_front_page_manual_items", "label": "Manual items", "name": "manual_items", "aria-label": "", "type": "repeater", "instructions": "", "required": 1, "conditional_logic": [[{"field": "field_front_page_manual_type", "operator": "==", "value": "manual"}]], "wrapper": {"width": "", "class": "", "id": ""}, "layout": "row", "min": 1, "max": 12, "collapsed": "", "button_label": "Add Row", "sub_fields": [{"key": "field_front_page_item_title", "label": "Title", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_front_page_item_summary", "label": "Summary", "name": "summary", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "rows": "", "placeholder": "", "new_lines": ""}, {"key": "field_front_page_item_image", "label": "Image", "name": "image", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "id", "library": "all", "preview_size": "medium"}, {"key": "field_front_page_item_button", "label": "<PERSON><PERSON>", "name": "button", "aria-label": "", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array"}]}]}, {"key": "field_front_page_button", "label": "<PERSON><PERSON>", "name": "front_page_button", "aria-label": "", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array"}], "location": [[{"param": "page_type", "operator": "==", "value": "front_page"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "Optional Featured Promo fields specifically for the front page", "show_in_rest": 0}]