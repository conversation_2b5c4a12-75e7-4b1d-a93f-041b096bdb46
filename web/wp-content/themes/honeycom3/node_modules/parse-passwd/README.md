# parse-passwd [![NPM version](https://img.shields.io/npm/v/parse-passwd.svg?style=flat)](https://www.npmjs.com/package/parse-passwd) [![NPM downloads](https://img.shields.io/npm/dm/parse-passwd.svg?style=flat)](https://npmjs.org/package/parse-passwd) [![Linux Build Status](https://img.shields.io/travis/doowb/parse-passwd.svg?style=flat&label=Travis)](https://travis-ci.org/doowb/parse-passwd) [![Windows Build Status](https://img.shields.io/appveyor/ci/doowb/parse-passwd.svg?style=flat&label=AppVeyor)](https://ci.appveyor.com/project/doowb/parse-passwd)

> Parse a passwd file into a list of users.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save parse-passwd
```

## Usage

```js
var parse = require('parse-passwd');
```

## API

**Example**

```js
// assuming '/etc/passwd' contains:
// doowb:*:123:123:Brian Woodward:/Users/<USER>/bin/bash
console.log(parse(fs.readFileSync('/etc/passwd', 'utf8')));

//=> [
//=>   {
//=>     username: 'doowb',
//=>     password: '*',
//=>     uid: '123',
//=>     gid: '123',
//=>     gecos: 'Brian Woodward',
//=>     homedir: '/Users/<USER>',
//=>     shell: '/bin/bash'
//=>   }
//=> ]
```

**Params**

* `content` **{String}**: Content of a passwd file to parse.
* `returns` **{Array}**: Array of user objects parsed from the content.

## About

### Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

Please read the [contributing guide](contributing.md) for avice on opening issues, pull requests, and coding standards.

### Building docs

_(This document was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme) (a [verb](https://github.com/verbose/verb) generator), please don't edit the readme directly. Any changes to the readme must be made in [.verb.md](.verb.md).)_

To generate the readme and API documentation with [verb](https://github.com/verbose/verb):

```sh
$ npm install -g verb verb-generate-readme && verb
```

### Running tests

Install dev dependencies:

```sh
$ npm install -d && npm test
```

### Author

**Brian Woodward**

* [github/doowb](https://github.com/doowb)
* [twitter/doowb](http://twitter.com/doowb)

### License

Copyright © 2016, [Brian Woodward](https://github.com/doowb).
Released under the [MIT license](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.2.0, on October 19, 2016._