# path-root [![NPM version](https://img.shields.io/npm/v/path-root.svg?style=flat)](https://www.npmjs.com/package/path-root) [![NPM downloads](https://img.shields.io/npm/dm/path-root.svg?style=flat)](https://npmjs.org/package/path-root) [![Build Status](https://img.shields.io/travis/jonschlinkert/path-root.svg?style=flat)](https://travis-ci.org/jonschlinkert/path-root)

> Get the root of a posix or windows filepath.

You might also be interested in [parse-filepath](https://github.com/jonschlinkert/parse-filepath).

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install path-root --save
```

## Usage

```js
var pathRoot = require('path-root');
```

**Examples**

```js
pathRoot('\\\\server\\share\\abc');
//=> '\\\\server\\share\\'

pathRoot('\\\\server foo\\some folder\\base-file.js');
//=> '\\\\server foo\\some folder\\'

pathRoot('\\\\?\\UNC\\server\\share');
//=> '\\\\?\\UNC\\'

pathRoot('foo/bar/baz.js');
//=> ''

pathRoot('c:\\foo\\bar\\baz.js');
//=> 'c:\\'

pathRoot('\\\\slslslsl\\admin$\\system32');
//=> '\\\\slslslsl\\admin$\\'

pathRoot('/foo/bar/baz.js');
//=> '/'
```

## Related projects

You might also be interested in these projects:

* [is-absolute](https://www.npmjs.com/package/is-absolute): Polyfill for node.js `path.isAbolute`. Returns true if a file path is absolute. | [homepage](https://github.com/jonschlinkert/is-absolute)
* [parse-filepath](https://www.npmjs.com/package/parse-filepath): Parse a filepath into an object. Falls back on the native node.js `path.parse` method if… [more](https://www.npmjs.com/package/parse-filepath) | [homepage](https://github.com/jonschlinkert/parse-filepath)
* [path-root-regex](https://www.npmjs.com/package/path-root-regex): Regular expression for getting the root of a posix or windows filepath. | [homepage](https://github.com/regexhq/path-root-regex)

## Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/path-root/issues/new).

## Building docs

Generate readme and API documentation with [verb](https://github.com/verbose/verb):

```sh
$ npm install verb && npm run docs
```

Or, if [verb](https://github.com/verbose/verb) is installed globally:

```sh
$ verb
```

## Running tests

Install dev dependencies:

```sh
$ npm install -d && npm test
```

## Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)

## License

Copyright © 2016, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT license](https://github.com/jonschlinkert/path-root/blob/master/LICENSE).

***

_This file was generated by [verb](https://github.com/verbose/verb), v, on March 29, 2016._