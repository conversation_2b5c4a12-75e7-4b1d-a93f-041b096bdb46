# is-relative [![NPM version](https://img.shields.io/npm/v/is-relative.svg?style=flat)](https://www.npmjs.com/package/is-relative) [![NPM monthly downloads](https://img.shields.io/npm/dm/is-relative.svg?style=flat)](https://npmjs.org/package/is-relative) [![NPM total downloads](https://img.shields.io/npm/dt/is-relative.svg?style=flat)](https://npmjs.org/package/is-relative) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/is-relative.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/is-relative)

> Returns `true` if the path appears to be relative.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save is-relative
```

## Usage

```js
var isRelative = require('is-relative');
console.log(isRelative('README.md'));
//=> true

console.log(isRelative('/User/dev/foo/README.md'));
//=> false
```

## About

### Related projects

* [is-absolute](https://www.npmjs.com/package/is-absolute): Polyfill for node.js `path.isAbolute`. Returns true if a file path is absolute. | [homepage](https://github.com/jonschlinkert/is-absolute "Polyfill for node.js `path.isAbolute`. Returns true if a file path is absolute.")
* [is-dotfile](https://www.npmjs.com/package/is-dotfile): Return true if a file path is (or has) a dotfile. Returns false if the… [more](https://github.com/jonschlinkert/is-dotfile) | [homepage](https://github.com/jonschlinkert/is-dotfile "Return true if a file path is (or has) a dotfile. Returns false if the path is a dot directory.")
* [is-glob](https://www.npmjs.com/package/is-glob): Returns `true` if the given string looks like a glob pattern or an extglob pattern… [more](https://github.com/jonschlinkert/is-glob) | [homepage](https://github.com/jonschlinkert/is-glob "Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a bet")
* [is-relative](https://www.npmjs.com/package/is-relative): Returns `true` if the path appears to be relative. | [homepage](https://github.com/jonschlinkert/is-relative "Returns `true` if the path appears to be relative.")
* [is-unc-path](https://www.npmjs.com/package/is-unc-path): Returns true if a filepath is a windows UNC file path. | [homepage](https://github.com/jonschlinkert/is-unc-path "Returns true if a filepath is a windows UNC file path.")

### Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

### Contributors

| **Commits** | **Contributor** | 
| --- | --- |
| 13 | [jonschlinkert](https://github.com/jonschlinkert) |
| 3 | [shinnn](https://github.com/shinnn) |

### Building docs

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

### Running tests

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

### Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)

### License

Copyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on July 13, 2017._