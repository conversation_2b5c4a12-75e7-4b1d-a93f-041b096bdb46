# is-negated-glob [![NPM version](https://img.shields.io/npm/v/is-negated-glob.svg?style=flat)](https://www.npmjs.com/package/is-negated-glob) [![NPM downloads](https://img.shields.io/npm/dm/is-negated-glob.svg?style=flat)](https://npmjs.org/package/is-negated-glob) [![Build Status](https://img.shields.io/travis/jonschlinkert/is-negated-glob.svg?style=flat)](https://travis-ci.org/jonschlinkert/is-negated-glob)

> Returns an object with a `negated` boolean and the `!` stripped from negation patterns. Also respects extglobs.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save is-negated-glob
```

## Usage

```js
var isNegatedGlob = require('is-negated-glob');

console.log(isNegatedGlob('foo'));
// { pattern: 'foo', negated: false }

console.log(isNegatedGlob('!foo'));
// { pattern: 'foo', negated: true }

console.log(isNegatedGlob('!(foo)'));
// extglob patterns are ignored
// { pattern: '!(foo)', negated: false }
```

## About

### Related projects

* [is-extglob](https://www.npmjs.com/package/is-extglob): Returns true if a string has an extglob. | [homepage](https://github.com/jonschlinkert/is-extglob "Returns true if a string has an extglob.")
* [is-glob](https://www.npmjs.com/package/is-glob): Returns `true` if the given string looks like a glob pattern or an extglob pattern… [more](https://github.com/jonschlinkert/is-glob) | [homepage](https://github.com/jonschlinkert/is-glob "Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a bet")
* [to-absolute-glob](https://www.npmjs.com/package/to-absolute-glob): Make a glob pattern absolute, ensuring that negative globs and patterns with trailing slashes are… [more](https://github.com/jonschlinkert/to-absolute-glob) | [homepage](https://github.com/jonschlinkert/to-absolute-glob "Make a glob pattern absolute, ensuring that negative globs and patterns with trailing slashes are correctly handled.")

### Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

### Building docs

_(This document was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme) (a [verb](https://github.com/verbose/verb) generator), please don't edit the readme directly. Any changes to the readme must be made in [.verb.md](.verb.md).)_

To generate the readme and API documentation with [verb](https://github.com/verbose/verb):

```sh
$ npm install -g verb verb-generate-readme && verb
```

### Running tests

Install dev dependencies:

```sh
$ npm install -d && npm test
```

### Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)

### License

Copyright © 2016, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT license](https://github.com/jonschlinkert/is-negated-glob/blob/master/LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.1.30, on September 08, 2016._