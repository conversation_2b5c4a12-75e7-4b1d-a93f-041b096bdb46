# object.defaults [![NPM version](https://img.shields.io/npm/v/object.defaults.svg?style=flat)](https://www.npmjs.com/package/object.defaults) [![NPM monthly downloads](https://img.shields.io/npm/dm/object.defaults.svg?style=flat)](https://npmjs.org/package/object.defaults)  [![NPM total downloads](https://img.shields.io/npm/dt/object.defaults.svg?style=flat)](https://npmjs.org/package/object.defaults) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/object.defaults.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/object.defaults)

> Like `extend` but only copies missing properties/values to the target object.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save object.defaults
```

Install with [bower](https://bower.io/)

```sh
$ bower install object.defaults --save
```

## Usage

```js
var defaults = require('object.defaults');

var obj = {a: 'c'};
defaults(obj, {a: 'bbb', d: 'c'});
console.log(obj);
//=> {a: 'c', d: 'c'}
```

Or immutable defaulting:
```js
var defaults = require('object.defaults/immutable');
var obj = {a: 'c'};
var defaulted = defaults(obj, {a: 'bbb', d: 'c'});
console.log(obj !== defaulted);
//=> true
```

## About

### Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

### Contributors

| **Commits** | **Contributor** | 
| --- | --- |
| 16 | [jonschlinkert](https://github.com/jonschlinkert) |
| 1 | [phated](https://github.com/phated) |
| 1 | [sobolevn](https://github.com/sobolevn) |

### Building docs

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

### Running tests

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

### Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)

### License

Copyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.5.0, on April 26, 2017._