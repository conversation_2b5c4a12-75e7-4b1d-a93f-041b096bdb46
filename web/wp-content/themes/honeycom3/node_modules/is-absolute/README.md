# is-absolute [![NPM version](https://img.shields.io/npm/v/is-absolute.svg?style=flat)](https://www.npmjs.com/package/is-absolute) [![NPM monthly downloads](https://img.shields.io/npm/dm/is-absolute.svg?style=flat)](https://npmjs.org/package/is-absolute) [![NPM total downloads](https://img.shields.io/npm/dt/is-absolute.svg?style=flat)](https://npmjs.org/package/is-absolute) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/is-absolute.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/is-absolute)

> Returns true if a file path is absolute. Does not rely on the path module and can be used as a polyfill for node.js native `path.isAbolute`.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save is-absolute
```

Originally based on the `isAbsolute` utility method in [express](https://github.com/visionmedia/express).

## Usage

```js
var isAbsolute = require('is-absolute');

isAbsolute('a/b/c.js');
//=> 'false'
isAbsolute('/a/b/c.js');
//=> 'true'
```

**Explicitly test windows paths**

```js
isAbsolute.posix('/foo/bar');
isAbsolute.posix('/user/docs/Letter.txt');
//=> true

isAbsolute.posix('foo/bar');
//=> false
```

**Explicitly test windows paths**

```js
var isAbsolute = require('is-absolute');

isAbsolute.win32('c:\\');
isAbsolute.win32('//C://user\\docs\\Letter.txt');
isAbsolute.win32('\\\\unc\\share');
isAbsolute.win32('\\\\unc\\share\\foo');
isAbsolute.win32('\\\\unc\\share\\foo\\');
isAbsolute.win32('\\\\unc\\share\\foo\\bar');
isAbsolute.win32('\\\\unc\\share\\foo\\bar\\');
isAbsolute.win32('\\\\unc\\share\\foo\\bar\\baz');
//=> true

isAbsolute.win32('a:foo/a/b/c/d');
isAbsolute.win32(':\\');
isAbsolute.win32('foo\\bar\\baz');
isAbsolute.win32('foo\\bar\\baz\\');
//=> false
```

## About

### Related projects

* [is-dotfile](https://www.npmjs.com/package/is-dotfile): Return true if a file path is (or has) a dotfile. Returns false if the… [more](https://github.com/jonschlinkert/is-dotfile) | [homepage](https://github.com/jonschlinkert/is-dotfile "Return true if a file path is (or has) a dotfile. Returns false if the path is a dot directory.")
* [is-glob](https://www.npmjs.com/package/is-glob): Returns `true` if the given string looks like a glob pattern or an extglob pattern… [more](https://github.com/jonschlinkert/is-glob) | [homepage](https://github.com/jonschlinkert/is-glob "Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a bet")
* [is-relative](https://www.npmjs.com/package/is-relative): Returns `true` if the path appears to be relative. | [homepage](https://github.com/jonschlinkert/is-relative "Returns `true` if the path appears to be relative.")
* [is-unc-path](https://www.npmjs.com/package/is-unc-path): Returns true if a filepath is a windows UNC file path. | [homepage](https://github.com/jonschlinkert/is-unc-path "Returns true if a filepath is a windows UNC file path.")
* [is-valid-glob](https://www.npmjs.com/package/is-valid-glob): Return true if a value is a valid glob pattern or patterns. | [homepage](https://github.com/jonschlinkert/is-valid-glob "Return true if a value is a valid glob pattern or patterns.")

### Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

### Contributors

| **Commits** | **Contributor** | 
| --- | --- |
| 35 | [jonschlinkert](https://github.com/jonschlinkert) |
| 1 | [es128](https://github.com/es128) |
| 1 | [shinnn](https://github.com/shinnn) |
| 1 | [Sobak](https://github.com/Sobak) |

### Building docs

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

### Running tests

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

### Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)

### License

Copyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on July 13, 2017._