<?php
/**
 * Single story
 *
 * @package honeycom3
 */
require_once get_template_directory() . '/classes/class-honeycom3-story.php';

if ( ! class_exists( 'Timber\Post' ) ) {
	die( 'Timber\Post cannot be found. Please fb-wp-hc3-theme-settings is installed and activated' );
}

$fb_story = ( isset( $fb_story ) && is_object( $fb_story ) ? $fb_story : new Honeycom3_Story() );

$context = Timber::context();

if ( post_password_required( $post->ID ) ) {
	Timber::render( '@wp/password-form-page.twig', $context );
} else {
	Timber::render( '@wp/stories/single.twig', $context, TIMBER_CACHE_TIME );
}
