{# {% block accordion %}
	{% for accordion in accordions %}
		{% if accordion.accordion_items %}
			{% set has_accordion_data = false %}
			{% for accordion_item in accordion.accordion_items %}
				{% if accordion_item.title or accordion_item.role or accordion_item.author_info or accordion_item.image or accordion_item.image_data %}
					{% set has_accordion_data = true %}
				{% endif %}
			{% endfor %}

			{% if has_accordion_data %}
				<div class="sidebar-accordion-outer">
					{% set accordion_items = accordion.accordion_items %}
					{% for accordion_item in accordion_items %}
						<div class="sidebar-accordion-item">
							<div class="sidebar-accordion-info">
								{% set image = accordion_item.image %}
								{% if accordion_item.image or accordion_item.image_data %}
									<div class="sidebar-accordion-info-image">
										{# Minimum image size: 100px X 100px
										{% set image_data = accordion_item.image_data %}
										{% if image or image_data %}
											{% include '@hc/partials/image.twig' with { 'imgheight': '100', 'imgwidth': '100' } %}
										{% endif %}
									</div>
								{% endif %}
								<div class="sidebar-accordion-info-inner">
									{% if accordion_item.title %}
										{{ accordion_item.title|e('html') }}
									{% endif %}
									{% if accordion_item.role %}
										<p class="sidebar-accordion-info-role">{{ accordion_item.role }}</p>
									{% endif %}
								</div>
							</div>

							{% set author_infos = accordion_item.author_info %}
							{% if accordion_item.author_info %}
								<button class="sidebar-accordion-button sidebar-accordion-trigger" aria-expanded="false" aria-controls="accordion-panel-{{ accordion_item.title|replace({' ': '-'}) }}"></button>
								<div class="sidebar-accordion-panel" aria-labelledby="accordion-panel-{{ accordion_item.title|replace({' ': '-'}) }}" aria-expanded="false" tabindex="-1">
									<div class="sidebar-accordion-additional-info">
										{% for author_info in author_infos %}
											<p class="sidebar-accordion-info-meta {{ author_info.selector }}">
												<a href="{% if author_info.selector == 'mail' %}mailto:{% endif %}{{ author_info.link }}">
													<span class="icon-outer">
														<svg aria-hidden="true" class="icon"><use xlink:href="{{ assets_dir }}/svg/icons.svg#{{ author_info.selector }}"/>{{ author_info.title }}</svg>
														<span class="visually-hidden">{{ author_info.title }}</span>
													</span>
													{{ author_info.title }}
												</a>
											</p>
										{% endfor %}
									</div>
								</div>
							{% endif %}
						</div>
					{% endfor %}
				</div>
			{% endif %}
		{% endif %}
	{% endfor %}
{% endblock accordion %}

{% block facility_accordion %}
	{% if facility_accordion %}
		<div class="facility-accordion">
			{% for accordion in accordions %}
				{% if accordion.accordion_list_items.0.title or accordion.accordion_list_items.0.file_name or accordion.summary %}
					{% block accordion_item %}
						<div class="accordion-item{% if accordion.type %} {{ accordion.type }}{% endif %}">
							{% block accordion_title %}
								<button class="accordion-button accordion-trigger active facility-accordion-button" aria-expanded="true" aria-controls="accordionPanel{{ accordion.title|replace({' ': ''}) }}">
									{{ accordion.title }}
									<svg aria-hidden="true" class="icon"><use xlink:href="{{ assets_dir }}/svg/icons.svg#arrow-right"/></svg>
								</button>
							{% endblock accordion_title %}

							{% block accordion_panel %}
								<div class="facility-accordion-panel" id="accordionPanel{{ accordion.title|replace({' ': ''}) }}" tabindex="-1" aria-expanded="true">

									{% set accordion_list_items = accordion.accordion_list_items %}
									{% if accordion_list_items %}
										<ul class="accordion-list">
											{% for accordion_list_item in accordion_list_items %}
												<li class="accordion-list-block">
													<p>{{ accordion_list_item.title }}
														{% if accordion_list_item.distance %}
															|
															{{ accordion_list_item.distance }}
														{% endif %}
													</p>
												</li>
											{% endfor %}
										</ul>
									{% endif %}

									{% if accordion.summary %}
										<div class="facility-accordion-info">
											{% if accordion.subtitle %}
												<p class="facility-accordion-subtitle">{{ accordion.subtitle }}</p>
											{% endif %}
											{% if accordion.summary %}
												<p class="facility-accordion-summary">{{ accordion.summary }}</p>
											{% endif %}
											{% if accordion.map_embed %}
												<div class="facility-map">{{ accordion.map_embed|raw }}</div>
											{% endif %}
											{% if accordion.map_path %}
												<a href="{{ accordion.map_path }}" class="facility-link" target="_blank">View directions on Google Maps</a>
											{% endif %}
											{% if accordion.what3words %}
												<p>
													<strong>What3words</strong>
													<a href="{{ accordion.what3words_path }}" target="_blank">{{ accordion.what3words }}</a>
												</p>
											{% endif %}
										</div>
									{% endif %}

								</div>
							{% endblock accordion_panel %}
						</div>
					{% endblock accordion_item %}
				{% endif %}
			{% endfor %}
		</div>
	{% endif %}
{% endblock facility_accordion %} #}

<ul class="sidebar-meta">
	{% if logo_image %}
		<li class="sidebar-meta-block image">
			{# Logo image from ACF field #}
			{% set image = get_attachment(logo_image) %}
			{% if image %}
				<img srcset="{{ image.srcset }}"
					 sizes="(min-width: 1280px) 608px, (min-width: 960px) 50vw, 100vw"
					 src="{{ image.src }}"
					 alt="{{ image.alt }}"
					 height="{{ image.height }}"
					 width="{{ image.width }}">
			{% endif %}
		</li>
	{% elseif image %}
		<li class="sidebar-meta-block image">
			{# Minimum image size: 325px X 433px #}
			{% include '@hc/partials/image.twig' %}
		</li>
	{% endif %}
	<li class="sidebar-meta-block info">
		<ul>
			{# {% if smartDate %}
				<li class="sidebar-meta-block date">
					<span>Date:</span>
					{{ smartDate }}
				</li>
			{% endif %} #}
			{% if date and not research_metadata and function('get_post_type') != 'research' %}
				<li class="sidebar-meta-block date">
					<span>Date:</span>
					{{ date|date("d/m/Y") }}
				</li>
			{% endif %}
			{% if author and not research_metadata and function('get_post_type') != 'research' %}
				<li class="sidebar-meta-block author">
					<span>Author:</span>
					{{ author }}
				</li>
			{% endif %}
			{% if startdate %}
				<li class="sidebar-meta-block startdate">
					<span>Date:</span>
					<time datetime="{{ startdate|date("Y-m-d") }}">
						{{ startdate|date("d/m/Y") }}</time>
					{% if enddate %}
						-
						<time datetime="{{ enddate|date("Y-m-d") }}">
							{{ enddate|date("d/m/Y") }}</time>
					{% endif %}
				</li>
			{% endif %}

			{% if startTime %}
				<li class="sidebar-meta-block time">
					<span>Time:</span>
					{{ startTime|date('H:i') }}
					{% if endTime %}
						-
						{{ endTime|date('H:i') }}
					{% endif %}
				</li>
			{% endif %}
			{% if location %}
				<li class="sidebar-meta-block location">
					<span>Location:</span>
					{{ location }}
				</li>
			{% endif %}

			{% if fee %}
				<li class="sidebar-meta-block fee">
					<span>Registration fee:</span>
					{{ fee|raw }}
				</li>
			{% endif %}
			{% if price %}
				<li class="sidebar-meta-block price">
					<span>Minimum fundraising pledge:</span>
					{{ price|raw }}
				</li>
			{% endif %}
			{% if distance %}
				<li class="sidebar-meta-block distance">
					<span>Distance:</span>
					{{ distance }}
				</li>
			{% endif %}
			{% if postbuttons %}
				<li class="sidebar-meta-block sidebar-button">
					{% for postbutton in postbuttons %}
						<a href="{{ postbutton.link }}" {% if postbutton.external_button %} target="_blank" {% endif %} class="button">{{ postbutton.title }}</a>
					{% endfor %}
				</li>
			{% endif %}
			{% if registration_link_copy %}
				<li class="sidebar-meta-block sidebar-button">
					<a href="{{ registration_link_copy.link }}"
					   {% if registration_link_copy.external %}target="_blank"{% endif %}
					   class="button-secondary">
						{{ registration_link_copy.title }}
					</a>
				</li>
			{% endif %}
			{% if postbtn %}
				<li class="sidebar-meta-block sidebar-button">
					{% for postbtn in postbtn %}
						<a href="{{ postbtn.link }}" {% if postbtn.external_button %} target="_blank" {% endif %} class="button-secondary">{{ postbtn.title }}</a>
					{% endfor %}
				</li>
			{% endif %}

			{% if authors %}
				{% for author in authors %}
					<li
						class="sidebar-meta-block author">
						{# Minimum image size: 100px X 100px #}
						{% set image = author.image %}
						{% set image_data = author.image_data %}
						{% if image or image_data %}
							<div class="author-image">
								{% include '@hc/partials/image.twig' with { 'imgheight': '100', 'imgwidth': '100' } %}
							</div>
						{% endif %}
						<div class="author-info">
							{% if author.url %}
								<p class="author-name">
									<a href="{{ author.url }}" {% if author.target %} target="{{ author.target }}" {% endif %}>{{ author.name }}</a>
								</p>
							{% else %}
								<p class="author-name">{{ author.name }}</p>
							{% endif %}
							<p>{{ author.role }}</p>
							<p>
								<a href="mailto:{{ author.email }}">{{ author.email }}</a>
							</p>
						</div>
					</li>
				{% endfor %}
			{% endif %}

			{% if research_metadata %}
				<li class="sidebar-meta-block research">
					{% if research_metadata.researchers %}
						<p><strong>Researchers</strong><br>
							{% for researcher in research_metadata.researchers %}
								{{ researcher }}<br>
							{% endfor %}
						</p>
					{% endif %}

					{% if research_metadata.location %}
						<p><strong>Location</strong><br>{{ research_metadata.location }}</p>
					{% endif %}

					{% if research_metadata.project_title %}
						<p><strong>Project Title</strong><br>{{ research_metadata.project_title }}</p>
					{% endif %}

					{% if research_metadata.value_of_grant %}
						<p><strong>Value of Grant</strong><br>£{{ research_metadata.value_of_grant }}</p>
					{% endif %}
				</li>
			{% endif %}



			{% if cats or tags %}
				<li class="sidebar-meta-block tags">
					{% if tag_title %}
						<h2 class="sidebar-meta-block-title">{{ tag_title }}</h2>
					{% endif %}
					<span class="sidebar-meta-block-inner">
						{% for cat in cats %}
							<a href="{{ cat.link }}" class="sidebar-meta-block-cat">{{ cat.title }}</a>
						{% endfor %}
					</span>
					<span class="sidebar-meta-block-inner">
						{% for tag in tags %}
							<a href="{{ tag.link }}" class="sidebar-meta-block-tag">{{ tag.title }}</a>
						{% endfor %}
					</span>
				</li>
			{% endif %}
			{% if postshares %}
				<li class="sidebar-meta-block share">
					{% if share_title %}
						<h2 class="sidebar-meta-share-title">{{ share_title }}</h2>
					{% endif %}
					{# {% if postshares %}
						<div class="sidebar-meta-share">
							{% for postshare in postshares %}
								<a href="{{ postshare.link }}" class="sidebar-meta-share-button {{ postshare.selector }}">
									<svg aria-hidden="true" class="icon"><use xlink:href="{{ assets_dir }}/svg/icons.svg#{{ postshare.selector }}"/>
										<span class="visually-hidden">{{ postshare.selector }}</span>
									</svg>
								</a>
							{% endfor %}
						</div>
					{% endif %} #}
				</li>
			{% endif %}
			{% if postdownloads %}
				<li class="sidebar-meta-block downloads">
					<h2 class="sidebar-meta-block-title">Downloads</h2>
					{% for postdownload in postdownloads %}
						<a href="{{ postdownload.link }}" {% if postdownload.target %} target="{{ postdownload.target }}" {% endif %} class="downloads-link">
							<span class="icon">
								<svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
									<path fill-rule="evenodd" clip-rule="evenodd" d="M1.63203 9.74941C2.07386 9.74941 2.43203 10.1076 2.43203 10.5494V13.7494C2.43203 13.9616 2.51632 14.1651 2.66635 14.3151C2.81638 14.4651 3.01986 14.5494 3.23203 14.5494H14.432C14.6442 14.5494 14.8477 14.4651 14.9977 14.3151C15.1477 14.1651 15.232 13.9616 15.232 13.7494V10.5494C15.232 10.1076 15.5902 9.74941 16.032 9.74941C16.4739 9.74941 16.832 10.1076 16.832 10.5494V13.7494C16.832 14.3859 16.5792 14.9964 16.1291 15.4465C15.679 15.8966 15.0686 16.1494 14.432 16.1494H3.23203C2.59551 16.1494 1.98506 15.8966 1.53497 15.4465C1.08489 14.9964 0.832031 14.3859 0.832031 13.7494V10.5494C0.832031 10.1076 1.1902 9.74941 1.63203 9.74941Z" fill="#124A9D"/>
									<path fill-rule="evenodd" clip-rule="evenodd" d="M4.26635 5.98373C4.57877 5.67131 5.0853 5.67131 5.39772 5.98373L8.83203 9.41804L12.2663 5.98373C12.5788 5.67131 13.0853 5.67131 13.3977 5.98373C13.7101 6.29615 13.7101 6.80268 13.3977 7.1151L9.39772 11.1151C9.0853 11.4275 8.57877 11.4275 8.26635 11.1151L4.26635 7.1151C3.95393 6.80268 3.95393 6.29615 4.26635 5.98373Z" fill="#124A9D"/>
									<path fill-rule="evenodd" clip-rule="evenodd" d="M8.83203 0.149414C9.27386 0.149414 9.63203 0.507586 9.63203 0.949414V10.5494C9.63203 10.9912 9.27386 11.3494 8.83203 11.3494C8.3902 11.3494 8.03203 10.9912 8.03203 10.5494V0.949414C8.03203 0.507586 8.3902 0.149414 8.83203 0.149414Z" fill="#124A9D"/>
								</svg>
							</span>
							{{ postdownload.linktext|default(postdownload.title)|e('html') }}

							{% if postdownload.size %}
								  <span>({{ postdownload.size }}{% if postdownload.type %}, {{ postdownload.type|upper }}{% endif %})</span>
							{% endif %}
						</a>
					{% endfor %}
				</li>
			{% endif %}





			{% if press_email or press_intro or press_tel or press_pack %}
				<li class="sidebar-meta-block press">
					<h2 class="sidebar-meta-block-title">{{ press_title }}</h2>

					{% if press_intro %}
						<p>{{ press_intro }}</p>
					{% endif %}
					{% if press_email %}
						<p class="press-mail">
							<a href="mailto:{{ press_email }}">
								<span class="icon-outer"></span>
								{{ press_email }}</a>
						</p>
					{% endif %}
					{% if press_tel %}
						<p class="press-tel">
							<a href="tel:{{ press_tel }}">
								<span class="icon-outer"></span>
								{{ press_tel }}</a>
						</p>
					{% endif %}
					{% if press_pack %}
						<p class="press-pack">
							<a href="{{ press_pack }}">
								<span class="icon-outer">
									<svg aria-hidden="true" class="icon"><use xlink:href="{{ assets_dir }}/svg/icons.svg#book-download"/></svg>
								</span>Download press pack</a>
						</p>
					{% endif %}
					{% if press_cta %}
						<p class="press-cta">
							<a href="{{ press_cta }}" class="press-cta-link">
								<strong>Share your story</strong>
							</a>
						</p>
					{% endif %}

				</li>
			{% endif %}
		</ul>
	</li>
</ul>


