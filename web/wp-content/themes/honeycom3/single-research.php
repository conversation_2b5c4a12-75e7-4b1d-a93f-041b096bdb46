<?php
/**
 * Single research
 *
 * @package honeycom3
 */
require_once get_template_directory() . '/classes/class-honeycom3-research.php';

if ( ! class_exists( '<PERSON>ber\Post' ) ) {
	die( 'Timber\Post cannot be found. Please fb-wp-hc3-theme-settings is installed and activated' );
}

$fb_research = ( isset( $fb_research ) && is_object( $fb_research ) ? $fb_research : new Honeycom3_Research() );

$context = Timber::context();

if ( post_password_required( $post->ID ) ) {
	Timber::render( '@wp/password-form-page.twig', $context );
} else {
	Timber::render( '@wp/research/single.twig', $context, TIMBER_CACHE_TIME );
}
