<?php
/**
 * Plugin Name: Honeycom3 Gutenberg Extensions
 * Plugin URI: http://fatbeehive.com
 * Description: Class for extending Gutenberg functionality without modifying submodules
 * Author: Fat Beehive
 * Author URI: http://fatbeehive.com
 * Version: 1.0
 *
 * @package FB WP HC3 Theme Settings
 */

/**
 * Class Honeycom3_Gutenberg_Extensions
 */
class Honeycom3_Gutenberg_Extensions {
	/**
	 * Constructor.
	 */
	public function __construct() {
		// Add hooks to extend submodule functionality
		add_filter('override_manual_featured_promo_item', array($this, 'extend_manual_featured_promo_item'), 10, 2);
		add_filter('override_automatic_featured_promo_item', array($this, 'extend_automatic_featured_promo_item'), 10, 2);

		// Add filter to handle empty featured promos
		add_filter('fb_gutenberg_twiggify', array($this, 'handle_empty_featured_promos'), 10, 2);

		// Advanced donation block hooks
		add_action('acf/init', array($this, 'register_advanced_donation_block'));
		add_filter('allowed_block_types_all', array($this, 'add_advanced_donation_block'), 20);


		// Gravity form customization
		add_filter('render_block_core/gravityforms', array($this, 'customize_gravity_form_wrapper'), 10, 2);

		// File download and profile data extensions
		add_filter('fb_gutenberg_file_download_data', array($this, 'extend_file_download_data'), 10, 2);
		add_filter('fb_gutenberg_profile_data', array($this, 'extend_profile_data'), 10, 2);
	}

	/**
	 * Extend manual featured promo item with additional data for update and library post types
	 *
	 * @param array $formatted_item The formatted item data
	 * @param array $fields The ACF fields data
	 * @return array The modified formatted item
	 */
	public function extend_manual_featured_promo_item($formatted_item, $fields) {

		// Get the post from the page ID if it exists in the fields
		if (isset($fields['manual_promos']['selected_content']) && isset($formatted_item['path'])) {
			$current_post = null;
			$current_post_id = null;

			foreach ($fields['manual_promos']['selected_content'] as $item) {
				if (isset($item['page'])) {
					$post = get_post($item['page']);
					if ($post && get_permalink($post->ID) === $formatted_item['path']) {
						$current_post = $post;
						$current_post_id = $post->ID;
						break;
					}
				}
			}

			if ($current_post) {
				$show_all_fields = !$fields['display_title_only'];
				$formatted_item = $this->process_post_type_data($formatted_item, $current_post->post_type, $current_post_id, $show_all_fields);
			}
		}

		return $formatted_item;
	}

	/**
	 * Handle empty featured promos
	 *
	 * @param mixed $twiggified_data The twiggified data from the submodule
	 * @param array $context The context data
	 * @return mixed The unmodified twiggified data
	 */
	public function handle_empty_featured_promos($twiggified_data, $context) {
		if (isset($context['block']['name']) && $context['block']['name'] === 'acf/featured-promos') {
			
			if ($twiggified_data === false) {
				
			}
		}
		return $twiggified_data;
	}

	/**
	 * Extend automatic featured promo item with additional data
	 *
	 * @param array $formatted_item The formatted item data
	 * @param object $post The post object
	 * @return array The modified formatted item
	 */
	public function extend_automatic_featured_promo_item($formatted_item, $post = null) {
		if ($post && isset($formatted_item['path'])) {
			$show_all_fields = true; // For automatic promos, we always show all fields
			$formatted_item = $this->process_post_type_data($formatted_item, $post->post_type, $post->ID, $show_all_fields);
		}

		return $formatted_item;
	}

	/**
	 * Process post type specific data for featured promos
	 *
	 * @param array $formatted_item The formatted item data
	 * @param string $post_type The post type
	 * @param int $post_id The post ID
	 * @param bool $show_all_fields Whether to show all fields
	 * @return array The modified formatted item
	 */
	private function process_post_type_data($formatted_item, $post_type, $post_id, $show_all_fields) {
		if ('update' === $post_type) {
			$formatted_item = $this->process_update_post_data($formatted_item, $post_id, $show_all_fields);
		} elseif ('library' === $post_type) {
			$formatted_item = $this->process_library_post_data($formatted_item, $post_id, $show_all_fields);
		}
		return $formatted_item;
	}

	/**
	 * Process update post data for featured promos
	 *
	 * @param array $formatted_item The formatted item data
	 * @param int $post_id The post ID
	 * @param bool $show_all_fields Whether to show all fields
	 * @return array The modified formatted item
	 */
	private function process_update_post_data($formatted_item, $post_id, $show_all_fields) {
		// Add update type as label
		$update_type = get_field('update_type', $post_id);
		if ($update_type) {
			$formatted_item['label'] = $update_type->name;
			$formatted_item['label_path'] = add_query_arg(array('update_type' => $update_type->term_id), get_post_type_archive_link('update'));
		}

		// Add date for update posts
		$formatted_item['date'] = get_the_date('U', $post_id);

		// Add tags for update posts
		if ($show_all_fields) {
			$update_themes = get_field('themes', $post_id);
			$update_topics = get_field('topics', $post_id);
			$tags = $this->build_tag_array($update_themes, $update_topics, 'update');

			if (!empty($tags)) {
				$formatted_item['tags'] = $tags;
			}
		}

		return $formatted_item;
	}

	/**
	 * Process library post data for featured promos
	 *
	 * @param array $formatted_item The formatted item data
	 * @param int $post_id The post ID
	 * @param bool $show_all_fields Whether to show all fields
	 * @return array The modified formatted item
	 */
	private function process_library_post_data($formatted_item, $post_id, $show_all_fields) {
		// Add library type as label
		$library_type = get_field('library_type', $post_id);
		if ($library_type) {
			$formatted_item['label'] = $library_type->name;
			$formatted_item['label_path'] = add_query_arg(array('library-type' => $library_type->term_id), get_post_type_archive_link('library'));
		}

		// Add tags for library posts
		if ($show_all_fields) {
			$library_themes = get_field('themes', $post_id);
			$library_topics = get_field('topics', $post_id);
			$tags = $this->build_tag_array($library_themes, $library_topics, 'library');

			if (!empty($tags)) {
				$formatted_item['tags'] = $tags;
			}
		}

		return $formatted_item;
	}

	/**
	 * Build tag array for themes and topics
	 *
	 * @param array $themes The themes array
	 * @param array $topics The topics array
	 * @param string $post_type The post type (update or library)
	 * @return array The tags array
	 */
	private function build_tag_array($themes, $topics, $post_type) {
		$tags = array();
		$i = 0;

		// Process themes
		if ($themes) {
			foreach ($themes as $theme) {
				$query_param = ('update' === $post_type) ? 'theme' : 'library-theme';
				$tags[$i]['path'] = add_query_arg(array($query_param => $theme->term_id), get_post_type_archive_link($post_type));
				$tags[$i]['title'] = $theme->name;
				++$i;
			}
		}

		// Process topics
		if ($topics) {
			foreach ($topics as $topic) {
				$query_param = ('update' === $post_type) ? 'topic' : 'library-topic';
				$tags[$i]['path'] = add_query_arg(array($query_param => $topic->term_id), get_post_type_archive_link($post_type));
				$tags[$i]['title'] = $topic->name;
				++$i;
			}
		}

		return $tags;
	}

	/**
	 * Register advanced donation block
	 *
	 */
	public function register_advanced_donation_block() {
		if (function_exists('register_block_type')) {
			// Register our block
			register_block_type(get_template_directory() . '/blocks/advanced-donation');
		}
	}

	/**
	 * Add advanced donation block to allowed blocks
	 *
	 * @param array $allowed_block_types The allowed block types
	 * @return array The modified allowed block types
	 */
	public function add_advanced_donation_block($allowed_block_types) {
		if (is_array($allowed_block_types)) {
			$allowed_block_types[] = 'acf/advanced-donation';
		}
		return $allowed_block_types;
	}

	/**
	 * Twiggify the data for advanced donation items.
	 * This method is used by the block template to format the data.
	 *
	 * @param array $fields The field data for the block.
	 * @return array $tabs Twiggified data as expected by FE template.
	 */
	public function twiggify_advanced_donation_items($fields) {
		$tabs = array();

		// Process donation types
		if (!empty($fields['donation_type'])) {
			// If donation type is "once" or "both", add single donation tab
			if ('once' === $fields['donation_type'] || 'both' === $fields['donation_type']) {
				$single_tab = array(
					'tab_name' => 'Once',
					'tab_url_string' => 'dontype=once',
					'currency_symbol' => '£',
					'amounts' => array(),
				);

				// Add single donation amounts
				if (!empty($fields['single_donation_suggested_amounts'])) {
					foreach ($fields['single_donation_suggested_amounts'] as $amount) {
						$single_tab['amounts'][] = array(
							'value' => esc_html($amount['amount']),
							'amount_description' => esc_html($amount['description']),
						);
					}
				}

				$tabs[] = $single_tab;
			}

			// If donation type is "monthly" or "both", add monthly donation tab
			if ('monthly' === $fields['donation_type'] || 'both' === $fields['donation_type']) {
				$monthly_tab = array(
					'tab_name' => 'Monthly',
					'tab_url_string' => 'dontype=monthly',
					'currency_symbol' => '£',
					'amounts' => array(),
				);

				// Add monthly donation amounts
				if (!empty($fields['monthly_donation_suggested_amounts'])) {
					foreach ($fields['monthly_donation_suggested_amounts'] as $amount) {
						$monthly_tab['amounts'][] = array(
							'value' => esc_html($amount['amount']),
							'amount_description' => esc_html($amount['description']),
						);
					}
				}

				$tabs[] = $monthly_tab;
			}
		}

		return $tabs;
	}

	/**
	 * Customize the Gravity Form wrapper
	 *
	 * @param string $block_content The block content
	 * @param array $block The block data (unused)
	 * @return string The modified form content
	 */
	public function customize_gravity_form_wrapper($block_content, $block = null) {
		// $block parameter is required by the filter but not used in this implementation
		return '<section class="form-outer"><div class="container"><div class="form-inner">' . $block_content . '</div></div></section>';
	}

	/**
	 * Extend file download data with additional information
	 *
	 * @param array $card The card data
	 * @param array $file_item The file item data
	 * @return array The modified card data
	 */
	public function extend_file_download_data($card, $file_item) {
		// Extract file type from filename
		if (isset($file_item['file']['filename'])) {
			$filename_parts = explode('.', $file_item['file']['filename']);
			$file_extension = false;
			if (is_array($filename_parts)) {
				$file_extensions_supported = array(
					'doc',
					'docx',
					'pdf',
					'ppt',
					'pptx',
					'xls',
					'xlsx',
					'zip',
				);
				$raw_file_extension = strtolower(end($filename_parts));
				if (in_array($raw_file_extension, $file_extensions_supported, true)) {
					$file_extension = $raw_file_extension;
				}
			}

			$card['type'] = $file_extension;
		}

		// Add image if available
		if (!empty($file_item['image'])) {
			$image_url = wp_get_attachment_image_url($file_item['image'], 'thumbnail');
			if ($image_url) {
				$card['image'] = esc_url($image_url);
			}
		}

		return $card;
	}

	/**
	 * Extend profile data with additional information
	 *
	 * @param array $card The card data
	 * @param array $item The profile item data
	 * @return array The modified card data
	 */
	public function extend_profile_data($card, $item) {
		// Use title field if available, otherwise fall back to role field
		if (isset($item['title']) && !empty($item['title'])) {
			$card['title'] = esc_html($item['title']);
		} elseif (isset($item['role']) && !empty($item['role'])) {
			$card['title'] = esc_html($item['role']);
		}

		return $card;
	}
}
