<?php
/**
 * Plugin Name: Gravity Forms WYSIWYG ACF Integration
 * Plugin URI: https://fatbeehive.com
 * Description: Adds a Gravity Forms button for WYSIWYG ACF components
 * Version: 1.0.0
 * Author: Dominic
 * Author URI: https://fatbeehive.com
 * License: GPL v2 or later
 * Text Domain: gf-wysiwyg-acf
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('GF_WYSIWYG_ACF_VERSION', '1.0.0');
define('GF_WYSIWYG_ACF_PLUGIN_URL', plugin_dir_url(__FILE__));
define('GF_WYSIWYG_ACF_PLUGIN_PATH', plugin_dir_path(__FILE__));

/**
 * Main plugin class
 */
class GF_WYSIWYG_ACF_Plugin {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
    }
    
    /**
     * Initialize the plugin
     */
    public function init() {
        // Check if Gravity Forms and ACF are active
        if (!$this->check_dependencies()) {
            add_action('admin_notices', array($this, 'dependency_notice'));
            return;
        }
        
        // Hook into ACF WYSIWYG editor
        add_action('acf/input/admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_filter('mce_buttons', array($this, 'add_tinymce_button'));
        add_filter('mce_external_plugins', array($this, 'add_tinymce_plugin'));
        
        // AJAX handlers
        add_action('wp_ajax_gf_get_forms', array($this, 'ajax_get_forms'));
        add_action('wp_ajax_gf_get_form_preview', array($this, 'ajax_get_form_preview'));

        // Add custom shortcode for wrapped forms
        add_shortcode('gf_custom_form', array($this, 'render_custom_form_shortcode'));
    }
    

    private function check_dependencies() {
        return class_exists('GFForms') && class_exists('ACF');
    }
    

    public function dependency_notice() {
        $missing = array();
        if (!class_exists('GFForms')) {
            $missing[] = 'Gravity Forms';
        }
        if (!class_exists('ACF')) {
            $missing[] = 'Advanced Custom Fields';
        }
        
        echo '<div class="notice notice-error"><p>';
        echo '<strong>Gravity Forms WYSIWYG ACF Integration:</strong> ';
        echo 'This plugin requires ' . implode(' and ', $missing) . ' to be installed and activated.';
        echo '</p></div>';
    }

    public function enqueue_scripts() {
        wp_enqueue_script(
            'gf-wysiwyg-acf-admin',
            GF_WYSIWYG_ACF_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            GF_WYSIWYG_ACF_VERSION,
            true
        );
        
        wp_enqueue_style(
            'gf-wysiwyg-acf-admin',
            GF_WYSIWYG_ACF_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            GF_WYSIWYG_ACF_VERSION
        );
        
        wp_localize_script('gf-wysiwyg-acf-admin', 'gfWysiwygAcf', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('gf_wysiwyg_acf_nonce'),
            'strings' => array(
                'selectForm' => __('Select a Gravity Form', 'gf-wysiwyg-acf'),
                'insertForm' => __('Insert Form', 'gf-wysiwyg-acf'),
                'cancel' => __('Cancel', 'gf-wysiwyg-acf'),
                'loading' => __('Loading...', 'gf-wysiwyg-acf'),
                'noForms' => __('No forms found. Please create a form first.', 'gf-wysiwyg-acf'),
                'error' => __('Error loading forms. Please try again.', 'gf-wysiwyg-acf'),
            )
        ));
    }
    
 
    public function add_tinymce_button($buttons) {
        array_push($buttons, 'gf_form_button');
        return $buttons;
    }
    
 
    public function add_tinymce_plugin($plugins) {
        $plugins['gf_form_button'] = GF_WYSIWYG_ACF_PLUGIN_URL . 'assets/js/tinymce-plugin.js';
        return $plugins;
    }
    
  
    public function ajax_get_forms() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'gf_wysiwyg_acf_nonce')) {
            wp_die('Security check failed');
        }
        
        if (!current_user_can('edit_posts')) {
            wp_die('Insufficient permissions');
        }
        
        $forms = GFAPI::get_forms();
        $form_options = array();
        
        foreach ($forms as $form) {
            $form_options[] = array(
                'id' => $form['id'],
                'title' => $form['title'],
                'description' => isset($form['description']) ? $form['description'] : '',
                'field_count' => count($form['fields'])
            );
        }
        
        wp_send_json_success($form_options);
    }
    
  
    public function ajax_get_form_preview() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'gf_wysiwyg_acf_nonce')) {
            wp_die('Security check failed');
        }
        
      
        if (!current_user_can('edit_posts')) {
            wp_die('Insufficient permissions');
        }
        
        $form_id = intval($_POST['form_id']);
        
        if (!$form_id) {
            wp_send_json_error('Invalid form ID');
        }
        
        $form = GFAPI::get_form($form_id);
        
        if (!$form) {
            wp_send_json_error('Form not found');
        }
        
        
        $preview_html = $this->generate_form_preview($form);
        
        wp_send_json_success(array(
            'preview' => $preview_html,
            'shortcode' => '[gf_custom_form id="' . $form_id . '" title="' . esc_attr($form['title']) . '" description="' . esc_attr($form['description']) . '"]'
        ));
    }
    
   
    private function generate_form_preview($form) {
        $html = '<div class="gf-preview-form">';
        $html .= '<h3>' . esc_html($form['title']) . '</h3>';
        
        if (!empty($form['description'])) {
            $html .= '<p class="gf-preview-description">' . esc_html($form['description']) . '</p>';
        }
        
        $html .= '<div class="gf-preview-fields">';
        
        $field_count = 0;
        foreach ($form['fields'] as $field) {
            if ($field_count >= 3) {
                $html .= '<p class="gf-preview-more">... and ' . (count($form['fields']) - 3) . ' more fields</p>';
                break;
            }
            
            $html .= '<div class="gf-preview-field">';
            $html .= '<label>' . esc_html($field->label);
            if ($field->isRequired) {
                $html .= ' <span class="gf-required">*</span>';
            }
            $html .= '</label>';
            
            switch ($field->type) {
                case 'text':
                case 'email':
                case 'phone':
                    $html .= '<input type="text" disabled placeholder="' . esc_attr($field->placeholder) . '">';
                    break;
                case 'textarea':
                    $html .= '<textarea disabled placeholder="' . esc_attr($field->placeholder) . '"></textarea>';
                    break;
                case 'select':
                    $html .= '<select disabled><option>Select an option</option></select>';
                    break;
                case 'radio':
                case 'checkbox':
                    $html .= '<div class="gf-preview-choices">';
                    $choice_count = 0;
                    foreach ($field->choices as $choice) {
                        if ($choice_count >= 2) {
                            $html .= '<span>...</span>';
                            break;
                        }
                        $html .= '<label><input type="' . $field->type . '" disabled> ' . esc_html($choice['text']) . '</label>';
                        $choice_count++;
                    }
                    $html .= '</div>';
                    break;
                default:
                    $html .= '<input type="text" disabled placeholder="' . esc_attr($field->type) . ' field">';
            }
            
            $html .= '</div>';
            $field_count++;
        }
        
        $html .= '</div>';
        $html .= '<div class="gf-preview-submit">';
        $html .= '<button type="button" disabled>' . esc_html($form['button']['text'] ?: 'Submit') . '</button>';
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }

  
    public function render_custom_form_shortcode($atts) {
        $atts = shortcode_atts(array(
            'id' => '',
            'title' => '',
            'description' => '',
            'ajax' => 'false',
            'tabindex' => ''
        ), $atts);

        if (empty($atts['id'])) {
            return '';
        }

        $form_id = intval($atts['id']);
        $form = GFAPI::get_form($form_id);

        if (!$form) {
            return '';
        }

        $title = !empty($atts['title']) ? $atts['title'] : $form['title'];
        $description = !empty($atts['description']) ? $atts['description'] : $form['description'];

        $gravity_form = gravity_form($form_id, false, false, false, '', $atts['ajax'] === 'true', $atts['tabindex'], false);

        $html = '<section class="form-outer section">';

        // Add header if title or description exists
        if (!empty($title) || !empty($description)) {
            $html .= '<header class="section-header container">';
            $html .= '<div class="section-header-inner">';

            if (!empty($title)) {
                $html .= '<h2 class="section-header-title">' . esc_html($title) . '</h2>';
            }

            if (!empty($description)) {
                $html .= '<div class="section-header-summary">' . wp_kses_post($description) . '</div>';
            }

            $html .= '</div>';
            $html .= '</header>';
        }

        // Add form container
        $html .= '<div class="container">';
        $html .= '<div class="form-inner">';
        $html .= $gravity_form;
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</section>';

        return $html;
    }
}

// Initialize the plugin
new GF_WYSIWYG_ACF_Plugin();
