/**
 * Gravity Forms WYSIWYG ACF Integration - Admin JavaScript
 */

(function($) {
    'use strict';

    var GFWysiwygAcf = {
        modal: null,
        selectedForm: null,
        currentEditor: null,

        init: function() {
            this.createModal();
            this.bindEvents();
        },

        createModal: function() {
            var modalHtml = `
                <div id="gf-modal-overlay" class="gf-modal-overlay">
                    <div class="gf-modal">
                        <div class="gf-modal-header">
                            <h2 class="gf-modal-title">${gfWysiwygAcf.strings.selectForm}</h2>
                            <button class="gf-modal-close" type="button">&times;</button>
                        </div>
                        <div class="gf-modal-body">
                            <div id="gf-forms-container">
                                <div class="gf-loading">${gfWysiwygAcf.strings.loading}</div>
                            </div>
                            <div id="gf-preview-container" class="gf-preview-container" style="display: none;">
                                <h3>Preview</h3>
                                <div id="gf-preview-content"></div>
                            </div>
                        </div>
                        <div class="gf-modal-footer">
                            <button id="gf-insert-form" class="gf-button" disabled>${gfWysiwygAcf.strings.insertForm}</button>
                            <button id="gf-cancel" class="gf-button gf-button-secondary">${gfWysiwygAcf.strings.cancel}</button>
                        </div>
                    </div>
                </div>
            `;

            $('body').append(modalHtml);
            this.modal = $('#gf-modal-overlay');
        },

        bindEvents: function() {
            var self = this;

            // Close modal events
            this.modal.on('click', '.gf-modal-close, #gf-cancel', function() {
                self.closeModal();
            });

            this.modal.on('click', '.gf-modal-overlay', function(e) {
                if (e.target === this) {
                    self.closeModal();
                }
            });

            // Form selection
            this.modal.on('click', '.gf-form-item', function() {
                self.selectForm($(this));
            });

            // Insert form
            this.modal.on('click', '#gf-insert-form', function() {
                self.insertForm();
            });

            // Escape key to close modal
            $(document).on('keydown', function(e) {
                if (e.keyCode === 27 && self.modal.is(':visible')) {
                    self.closeModal();
                }
            });
        },

        openModal: function(editor) {
            this.currentEditor = editor;
            this.selectedForm = null;
            this.loadForms();
            this.modal.show();
            $('#gf-preview-container').hide();
            $('#gf-insert-form').prop('disabled', true);
        },

        closeModal: function() {
            this.modal.hide();
            this.currentEditor = null;
            this.selectedForm = null;
        },

        loadForms: function() {
            var self = this;

            $.ajax({
                url: gfWysiwygAcf.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'gf_get_forms',
                    nonce: gfWysiwygAcf.nonce
                },
                success: function(response) {
                    if (response.success) {
                        self.renderForms(response.data);
                    } else {
                        self.showError(gfWysiwygAcf.strings.error);
                    }
                },
                error: function() {
                    self.showError(gfWysiwygAcf.strings.error);
                }
            });
        },

        renderForms: function(forms) {
            var container = $('#gf-forms-container');

            if (forms.length === 0) {
                container.html(`<div class="gf-no-forms">${gfWysiwygAcf.strings.noForms}</div>`);
                return;
            }

            var html = '<div class="gf-forms-list">';
            
            forms.forEach(function(form) {
                html += `
                    <div class="gf-form-item" data-form-id="${form.id}">
                        <h4 class="gf-form-item-title">${form.title}</h4>
                        ${form.description ? `<p class="gf-form-item-description">${form.description}</p>` : ''}
                        <div class="gf-form-item-meta">
                            ID: ${form.id} | Fields: ${form.field_count}
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            container.html(html);
        },

        selectForm: function($formItem) {
            var formId = $formItem.data('form-id');
            
            // Update selection
            $('.gf-form-item').removeClass('selected');
            $formItem.addClass('selected');
            
            this.selectedForm = formId;
            $('#gf-insert-form').prop('disabled', false);
            
            // Load preview
            this.loadFormPreview(formId);
        },

        loadFormPreview: function(formId) {
            var self = this;

            $.ajax({
                url: gfWysiwygAcf.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'gf_get_form_preview',
                    form_id: formId,
                    nonce: gfWysiwygAcf.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $('#gf-preview-content').html(response.data.preview);
                        $('#gf-preview-container').show();
                        self.selectedFormShortcode = response.data.shortcode;
                    }
                },
                error: function() {
                    console.error('Error loading form preview');
                }
            });
        },

        insertForm: function() {
            if (!this.selectedForm || !this.currentEditor) {
                return;
            }

            var shortcode = this.selectedFormShortcode || `[gf_custom_form id="${this.selectedForm}"]`;
            
            // Insert shortcode into editor
            if (typeof this.currentEditor.insertContent === 'function') {
                this.currentEditor.insertContent(shortcode);
            } else if (typeof this.currentEditor.execCommand === 'function') {
                this.currentEditor.execCommand('mceInsertContent', false, shortcode);
            }

            this.closeModal();
        },

        showError: function(message) {
            $('#gf-forms-container').html(`<div class="gf-error">${message}</div>`);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        GFWysiwygAcf.init();
    });

    // Make it globally accessible for TinyMCE
    window.GFWysiwygAcf = GFWysiwygAcf;

})(jQuery);
