/* Gravity Forms WYSIWYG ACF Integration Styles */

/* Modal Styles */
.gf-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 100000;
    display: none;
}

.gf-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 800px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    z-index: 100001;
}

.gf-modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid #ddd;
    background: #f9f9f9;
}

.gf-modal-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #23282d;
}

.gf-modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gf-modal-close:hover {
    color: #000;
}

.gf-modal-body {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
}

.gf-modal-footer {
    padding: 20px 24px;
    border-top: 1px solid #ddd;
    background: #f9f9f9;
    text-align: right;
}

/* Form Selection */
.gf-form-selector {
    margin-bottom: 20px;
}

.gf-form-selector label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #23282d;
}

.gf-form-selector select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

/* Form List */
.gf-forms-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

.gf-form-item {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #fff;
}

.gf-form-item:hover {
    border-color: #0073aa;
    box-shadow: 0 2px 8px rgba(0, 115, 170, 0.1);
}

.gf-form-item.selected {
    border-color: #0073aa;
    background: #f0f8ff;
}

.gf-form-item-title {
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #23282d;
}

.gf-form-item-description {
    color: #666;
    font-size: 13px;
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.gf-form-item-meta {
    font-size: 12px;
    color: #999;
}

/* Form Preview */
.gf-preview-container {
    margin-top: 20px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f9f9f9;
}

.gf-preview-form {
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    border: 1px solid #e1e1e1;
}

.gf-preview-form h3 {
    margin: 0 0 12px 0;
    color: #23282d;
    font-size: 16px;
}

.gf-preview-description {
    margin: 0 0 16px 0;
    color: #666;
    font-size: 14px;
}

.gf-preview-fields {
    margin-bottom: 16px;
}

.gf-preview-field {
    margin-bottom: 16px;
}

.gf-preview-field label {
    display: block;
    margin-bottom: 4px;
    font-weight: 600;
    color: #23282d;
    font-size: 14px;
}

.gf-required {
    color: #d63638;
}

.gf-preview-field input,
.gf-preview-field textarea,
.gf-preview-field select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background: #f9f9f9;
}

.gf-preview-field textarea {
    height: 80px;
    resize: vertical;
}

.gf-preview-choices {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.gf-preview-choices label {
    display: flex;
    align-items: center;
    font-weight: normal;
    margin: 0;
}

.gf-preview-choices input {
    width: auto;
    margin-right: 8px;
}

.gf-preview-more {
    color: #666;
    font-style: italic;
    margin: 8px 0;
}

.gf-preview-submit button {
    background: #0073aa;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    font-size: 14px;
    cursor: not-allowed;
    opacity: 0.7;
}

/* Loading and Error States */
.gf-loading {
    text-align: center;
    padding: 40px;
    color: #666;
}

.gf-error {
    color: #d63638;
    background: #fcf0f1;
    border: 1px solid #d63638;
    border-radius: 4px;
    padding: 12px;
    margin: 16px 0;
}

.gf-no-forms {
    text-align: center;
    padding: 40px;
    color: #666;
}

/* Buttons */
.gf-button {
    background: #0073aa;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: background-color 0.2s ease;
}

.gf-button:hover {
    background: #005a87;
    color: #fff;
}

.gf-button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.gf-button-secondary {
    background: #fff;
    color: #0073aa;
    border: 1px solid #0073aa;
}

.gf-button-secondary:hover {
    background: #f0f8ff;
    color: #005a87;
}

/* TinyMCE Button */
.mce-i-gf-form-icon:before {
    content: "\f314";
    font-family: dashicons;
}

/* Responsive */
@media (max-width: 768px) {
    .gf-modal {
        width: 95%;
        max-height: 90vh;
    }
    
    .gf-forms-list {
        grid-template-columns: 1fr;
    }
    
    .gf-modal-body {
        padding: 16px;
    }
    
    .gf-modal-header,
    .gf-modal-footer {
        padding: 16px;
    }
}
